#example 3x3 rubik cube
import FreeCAD as App
import Part
import Import
import os

# =========================
# Document Setup
# =========================
doc = App.newDocument("GeneratedModel")
doc.Label = "3x3 LEGO Brick Stack on 5x5 Plate"

# =========================
# Parameters (mm)
# =========================
plate_length = 40.0
plate_width  = 40.0
plate_height = 3.2

brick_length = 24.0
brick_width  = 24.0
brick_height = 9.6   # Single brick height
brick_count  = 3     # Number of bricks stacked

# Computed
stack_height = brick_height * brick_count  # 28.8 mm
brick_z_pos  = plate_height                # Brick stack sits atop the plate

# Center offsets to align center of brick stack with center of plate
plate_center_x = plate_length / 2.0
plate_center_y = plate_width  / 2.0
brick_center_x = brick_length / 2.0
brick_center_y = brick_width  / 2.0

# =========================
# Create 5x5 Plate
# =========================
plate = Part.makeBox(plate_length,
                     plate_width,
                     plate_height,
                     App.Vector(0, 0, 0))
plate_feature = doc.addObject("Part::Feature", "Plate5x5")
plate_feature.Label = "5x5 Plate"
plate_feature.Shape = plate

# =========================
# Create 3x3 Brick Stack
# =========================
# Single box with total height = 3 bricks * brick_height
brick_stack = Part.makeBox(brick_length,
                           brick_width,
                           stack_height,
                           App.Vector(
                               plate_center_x - brick_center_x,
                               plate_center_y - brick_center_y,
                               brick_z_pos
                           ))
brick_feature = doc.addObject("Part::Feature", "BrickStack3x3")
brick_feature.Label = "3x3 Brick Stack (3 high)"
brick_feature.Shape = brick_stack

# =========================
# Boolean Fuse Operation
# =========================
# Fuse plate and brick stack into one assembly
fused_shape = plate_feature.Shape.fuse(brick_feature.Shape)
assembly = doc.addObject("Part::Feature", "StackedAssembly")
assembly.Label = "Stacked Assembly"
assembly.Shape = fused_shape

# Recompute document to finalize operations
doc.recompute()

# =========================
# Export to STEP
# =========================
# Prepare output directory
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
step_path = os.path.join(output_dir,
                         "3x3_LEGO_brick_stack_on_5x5_LEGO_plate.step")

# Export only the final assembly
Import.export([assembly], step_path)

# Final recompute for safety
doc.recompute()

#example Cone with one hole
import FreeCAD as App
import Part
import Import

# Parameters (for easy modification)
R_CONE = 20.0     # Base radius of the cone
H_CONE = 40.0     # Height of the cone
R_HOLE = 5.0      # Radius of the cylindrical hole
H_HOLE = 40.0     # Height of the cylindrical hole (same as cone)

# Create a new document
doc = App.newDocument("GeneratedModel")
App.ActiveDocument.Label = "Cone_with_one_hole"

# -------------------------------------------------------------------
# 1. Create the base cone
# -------------------------------------------------------------------
# Cone with base radius R_CONE, top radius 0, height H_CONE,
# placed at the origin, axis aligned with Z
cone_shape = Part.makeCone(R_CONE, 0.0, H_CONE,
                           App.Vector(0, 0, 0),
                           App.Vector(0, 0, 1))
cone_obj = doc.addObject("Part::Feature", "Cone")
cone_obj.Shape = cone_shape

# -------------------------------------------------------------------
# 2. Create the cutting cylinder (the hole)
# -------------------------------------------------------------------
# Cylinder of radius R_HOLE, height H_HOLE,
# placed at the origin, axis aligned with Z
cyl_shape = Part.makeCylinder(R_HOLE, H_HOLE,
                              App.Vector(0, 0, 0),
                              App.Vector(0, 0, 1))
cyl_obj = doc.addObject("Part::Feature", "Hole_Cylinder")
cyl_obj.Shape = cyl_shape

# Recompute to register the shapes
doc.recompute()

# -------------------------------------------------------------------
# 3. Perform the boolean cut operation
# -------------------------------------------------------------------
# Subtract the cylinder from the cone
cut_shape = cone_shape.cut(cyl_shape)

# Create a new object to hold the result
result = doc.addObject("Part::Feature", "Cone_with_hole")
result.Shape = cut_shape

# -------------------------------------------------------------------
# 4. Finalize the model
# -------------------------------------------------------------------
doc.recompute()

# -------------------------------------------------------------------
# 5. Export to STEP
# -------------------------------------------------------------------
# Ensure the output directory 'cad_outputs' exists before exporting.
step_path = "cad_outputs/Cone_with_one_hole.step"
Import.export([result], step_path)

# End of script. The model "Cone_with_one_hole" has been created and exported.

#example Hexagonal
import FreeCAD as App
import Part
import Draft
import Import
import os

# Parameters
radius = 10.0    # Radius of circumscribed circle for hexagon
height = 5.0     # Extrusion height (prism height)
doc_name = "GeneratedModel"
output_dir = "cad_outputs"
step_filename = "Hexagonal_Prism.step"

# Create new FreeCAD document
doc = App.newDocument(doc_name)
doc.Label = "Hexagonal Prism"

# Create a regular hexagon (wire) in the XY plane
hexagon = Draft.makePolygon(6, radius)
# Ensure the document is up to date so Shape is available
doc.recompute()

# Convert the hexagon wire into a planar face
hex_face = Part.Face(hexagon.Shape)

# Extrude the face along Z to create a prism
prism_shape = hex_face.extrude(App.Vector(0, 0, height))

# Add the prism to the document as a Part::Feature
hex_prism = doc.addObject("Part::Feature", "HexagonalPrism")
hex_prism.Shape = prism_shape

# Remove the original wire object to keep the document clean
doc.removeObject(hexagon.Name)

# Final recompute to ensure the model is up to date
doc.recompute()

# Prepare output directory
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Export the hexagonal prism to STEP
export_path = os.path.join(output_dir, step_filename)
Import.export([hex_prism], export_path)

# Final document recompute
doc.recompute()

#example lego 3x3 brick
import os
import FreeCAD as App
import Part
import Import

# ------------------------------------------------------------
# Script: Lego_3x3_Brick.py
# Description: Generates a standard 3x3 LEGO brick (24×24×9.6 mm)
#              with nine studs (Ø4.8×1.8 mm) and exports to STEP.
# Environment: freecadcmd, FreeCAD 1.0.0 API
# ------------------------------------------------------------

# 1. Setup document
doc = App.newDocument("GeneratedModel")
doc.Label = "Lego_3x3_Brick"

# 2. Define key parameters (all units in mm)
brick_length = 24.0
brick_width  = 24.0
brick_height = 9.6

stud_radius = 2.4
stud_height = 1.8

# Stud positions relative to brick origin (centered grid at top face)
stud_positions = [
    (-8.0, -8.0),
    ( 0.0, -8.0),
    ( 8.0, -8.0),
    (-8.0,  0.0),
    ( 0.0,  0.0),
    ( 8.0,  0.0),
    (-8.0,  8.0),
    ( 0.0,  8.0),
    ( 8.0,  8.0),
]

# 3. Create base brick body
brick_shape = Part.makeBox(brick_length,
                           brick_width,
                           brick_height,
                           App.Vector(0, 0, 0))

# 4. Create and fuse each stud onto the brick
#    We'll build up the brick_shape variable by successive fuses
for idx, (sx, sy) in enumerate(stud_positions, start=1):
    # Create a single stud (cylinder)
    stud = Part.makeCylinder(
        stud_radius,
        stud_height,
        App.Vector(sx + brick_length/2.0,
                   sy + brick_width/2.0,
                   brick_height),
        App.Vector(0, 0, 1)
    )
    # Fuse stud into the brick
    brick_shape = brick_shape.fuse(stud)

# 5. Add final fused shape to the document
brick_obj = doc.addObject("Part::Feature", "Lego_3x3_Brick")
brick_obj.Label = "Lego 3x3 Brick"
brick_obj.Shape = brick_shape

# 6. Final recompute
doc.recompute()

# 7. Export to STEP
output_dir = "cad_outputs"
if not os.path.isdir(output_dir):
    os.makedirs(output_dir)

step_path = os.path.join(output_dir, "Lego_3x3_Brick.step")
Import.export([brick_obj], step_path)

# 8. Final recompute (post-export, if needed)
doc.recompute()

# End of script

#example L-shape Bracket
import os
import FreeCAD as App
import Part
import Import

# Ensure output directory exists
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Create new document
doc = App.newDocument("GeneratedModel")
doc.Label = "L-shaped_bracket"

# Parameters
thickness = 6.0
leg_length = 90.0
hole_radius = 5.25
# Horizontal plate: dimensions 90 x 90 x 6
# Vertical plate: dimensions 6 x 90 x 90 (attached along one 90x6 face)

# 1) Create the two primary boxes
box1 = Part.makeBox(leg_length, leg_length, thickness, App.Vector(0, 0, 0))
box2 = Part.makeBox(thickness, leg_length, leg_length, App.Vector(0, 0, 0))

# 2) Fuse them into the L-shaped bracket
bracket_shape = box1.fuse(box2)

# 3) Define cylinder positions (center coordinates) for 8 holes
#    First 4: through horizontal plate (axis along Z)
h_hole_centers = [
    (15.0, 15.0, thickness/2.0),
    (75.0, 15.0, thickness/2.0),
    (15.0, 75.0, thickness/2.0),
    (75.0, 75.0, thickness/2.0),
]
#    Next 4: through vertical plate (axis along X)
v_hole_centers = [
    (thickness/2.0, 15.0, 15.0),
    (thickness/2.0, 75.0, 15.0),
    (thickness/2.0, 15.0, 75.0),
    (thickness/2.0, 75.0, 75.0),
]

# 4) Cut holes in the bracket
# Horizontal holes
for idx, (cx, cy, cz) in enumerate(h_hole_centers, start=1):
    # For a hole through the horizontal plate, cylinder axis = Z.
    # Base point z = cz - hole_height/2 = (thickness/2 - thickness/2) = 0
    base_pt = App.Vector(cx, cy, 0.0)
    cyl = Part.makeCylinder(hole_radius, thickness, base_pt, App.Vector(0, 0, 1))
    bracket_shape = bracket_shape.cut(cyl)

# Vertical holes
for idx, (cx, cy, cz) in enumerate(v_hole_centers, start=1):
    # For a hole through the vertical plate, cylinder axis = X.
    # Base point x = cx - hole_height/2 = 0
    base_pt = App.Vector(0.0, cy, cz)
    cyl = Part.makeCylinder(hole_radius, thickness, base_pt, App.Vector(1, 0, 0))
    bracket_shape = bracket_shape.cut(cyl)

# 5) Create a Part Feature and assign final shape
bracket_obj = doc.addObject("Part::Feature", "Bracket")
bracket_obj.Label = "L-shaped_bracket"
bracket_obj.Shape = bracket_shape

# Recompute document to finalize geometry
doc.recompute()

# 6) Export the final bracket to STEP
step_path = os.path.join(output_dir, "L-shaped_bracket.step")
Import.export([bracket_obj], step_path)

print(f"Model generated and exported to: {step_path}"0)

#example Airfoil NACA 2412 
import FreeCAD as App
import Part
import math
import os
import Import

# ---------------------------------------
# Parameters (easy tuning at top)
# ---------------------------------------
chord     = 1.0    # chord length
span      = 5.0    # wing span (extrusion length)
m         = 0.02   # max camber (2% of chord)
p         = 0.4    # camber position (40% of chord)
t         = 0.12   # max thickness (12% of chord)
n_points  = 100    # number of discretization points per surface

# -------------------------------
# 1) Create new, headless document
# -------------------------------
doc = App.newDocument("GeneratedModel")
doc.Label = "NACA_2412_Airfoil_Extrusion"

# ---------------------------------------
# 2) Define function to compute NACA 4-digit coordinates
# ---------------------------------------
def naca4_coordinates(m, p, t, chord, n):
    """
    Returns two lists of App.Vector: (upper_surface_pts, lower_surface_pts)
    representing the cambered airfoil profile in the X–Z plane.
    """
    upper = []
    lower = []
    for i in range(n):
        # Cosine spacing for better resolution near leading/trailing edge
        beta = math.pi * i / (n - 1)
        x = 0.5 * (1.0 - math.cos(beta)) * chord
        # Thickness distribution (half-thickness)
        yt = (t / 0.2) * chord * (
              0.2969 * math.sqrt(x / chord)
            - 0.1260 * (x / chord)
            - 0.3516 * (x / chord)**2
            + 0.2843 * (x / chord)**3
            - 0.1015 * (x / chord)**4
        )
        # Camber line y_c and slope dy_c/dx
        xc = x / chord
        if xc <= p:
            yc = (m / p**2) * (2*p*xc - xc**2) * chord
            dyc_dx = (2*m / p**2) * (p - xc)
        else:
            yc = (m / (1 - p)**2) * ((1 - 2*p) + 2*p*xc - xc**2) * chord
            dyc_dx = (2*m / (1 - p)**2) * (p - xc)
        theta = math.atan(dyc_dx)
        # Upper surface (x_u, z_u)
        xu = x - yt * math.sin(theta)
        zu = yc + yt * math.cos(theta)
        # Lower surface (x_l, z_l)
        xl = x + yt * math.sin(theta)
        zl = yc - yt * math.cos(theta)
        upper.append(App.Vector(xu, 0.0, zu))
        lower.append(App.Vector(xl, 0.0, zl))
    return upper, lower

# ---------------------------------------
# 3) Generate the airfoil outline
# ---------------------------------------
pts_up, pts_lo = naca4_coordinates(m, p, t, chord, n_points)

# Force exact closure at trailing edge
te = App.Vector(chord, 0.0, 0.0)
pts_up[-1] = te
pts_lo[-1] = te

# Build closed loop: start TE lower → LE → TE upper
outline = list(reversed(pts_lo)) + pts_up

# ---------------------------------------
# 4) Create wire & face from outline
# ---------------------------------------
wire = Part.makePolygon(outline)
face = Part.Face(wire)

# ---------------------------------------
# 5) Extrude face along Y-axis (span)
# ---------------------------------------
airfoil_solid = face.extrude(App.Vector(0.0, span, 0.0))

# ---------------------------------------
# 6) Add Part::Feature to document
# ---------------------------------------
airfoil_obj = doc.addObject("Part::Feature", "NACA2412_Airfoil")
airfoil_obj.Label = "NACA 2412 Airfoil"
airfoil_obj.Shape = airfoil_solid

# ---------------------------------------
# 7) Final recompute
# ---------------------------------------
doc.recompute()

# ---------------------------------------
# 8) Export to STEP
# ---------------------------------------
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
step_path = os.path.join(output_dir, "NACA_2412_Airfoil_Extrusion.step")
Import.export([airfoil_obj], step_path)
print(f"Model exported to: {step_path}")

#example Rectangular with 5 straight through holes
import os
import FreeCAD as App
import Part
import Import

# ------------------------------------------------------------
# Script: Rectangular_block_with_5_straight_through_holes.py
# Description: Generate a rectangular block with 5 straight
# through holes, then export as STEP. Compatible with freecadcmd.
# ------------------------------------------------------------

# Create document
doc = App.newDocument("GeneratedModel")
doc.Label = "Rectangular block with 5 straight through holes"

# Parameters (for easy modification)
length = 100.0   # X dimension of the box
width  = 50.0    # Y dimension of the box
height = 20.0    # Z dimension of the box

hole_radius = 5.0
hole_height = height  # through-hole

# X-positions of holes (evenly spaced)
hole_x_positions = [10.0, 30.0, 50.0, 70.0, 90.0]
hole_y = width / 2.0   # centered in Y
hole_z = 0.0           # start at base of the box

# 1) Create the main rectangular block
main_box_shape = Part.makeBox(length, width, height, App.Vector(0, 0, 0))
# We'll apply boolean cuts to this shape iteratively
current_shape = main_box_shape

# 2) Create cylinders for holes and subtract them one by one
for idx, x in enumerate(hole_x_positions, start=1):
    cyl = Part.makeCylinder(hole_radius, hole_height, App.Vector(x, hole_y, hole_z))
    # Subtract this hole from the current block shape
    current_shape = current_shape.cut(cyl)

# 3) Add final shape to document
final_obj = doc.addObject("Part::Feature", "Block_with_5_Holes")
final_obj.Label = "RectBlockWithHoles"
final_obj.Shape = current_shape

# 4) Recompute document to finalize operations
doc.recompute()

# 5) Ensure export directory exists
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 6) Export the final object to STEP
step_path = os.path.join(output_dir,
    "Rectangular_block_with_5_straight_through_holes.step")
Import.export([final_obj], step_path)

# Final recompute (just in case)
doc.recompute()

#example Rectangular with central and corner holes
import os
import FreeCAD as App
import Part
import Import

# Ensure output directory exists
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Create a new document
doc = App.newDocument("GeneratedModel")
doc.Label = "Rectangular_block_with_central_and_corner_through_holes"

# Parameters
length = 100.0
width = 50.0
height = 10.0
hole_radius = 5.0
# Positions for holes (Z is 0 so they go through entire height)
central_hole_pos = App.Vector(length/2.0, width/2.0, 0)
corner_offsets = [
    App.Vector(10.0, 10.0, 0),
    App.Vector(length - 10.0, 10.0, 0),
    App.Vector(10.0, width - 10.0, 0),
    App.Vector(length - 10.0, width - 10.0, 0),
]

# 1) Create the base block
base_box = Part.makeBox(length, width, height, App.Vector(0, 0, 0))
base_obj = doc.addObject("Part::Feature", "BaseBlock")
base_obj.Shape = base_box

# 2) Create the central through-hole cylinder
cyl_central = Part.makeCylinder(hole_radius, height, central_hole_pos, App.Vector(0, 0, 1))
cyl_central_obj = doc.addObject("Part::Feature", "CentralHole")
cyl_central_obj.Shape = cyl_central

# Cut central hole
cut1 = base_obj.Shape.cut(cyl_central_obj.Shape)
base_obj.Shape = cut1
doc.recompute()

# 3) Create and cut corner holes one by one
for idx, pos in enumerate(corner_offsets, start=1):
    cyl = Part.makeCylinder(hole_radius, height, pos, App.Vector(0, 0, 1))
    cyl_obj = doc.addObject("Part::Feature", f"CornerHole{idx}")
    cyl_obj.Shape = cyl
    # Perform cut on the current base shape
    new_shape = base_obj.Shape.cut(cyl_obj.Shape)
    base_obj.Shape = new_shape
    doc.recompute()

# Rename final object
base_obj.Label = "FinalBlock"

# Final recompute
doc.recompute()

# Export to STEP
step_filepath = os.path.join(output_dir,
    "Rectangular_block_with_central_and_corner_through_holes.step")
Import.export([base_obj], step_filepath)

print(f"Model exported to {step_filepath}")

#Example Rectangular with 
import FreeCAD as App
import Part
import math
import os
import Import

# Create a new document
doc = App.newDocument("GeneratedModel")
doc.Label = "Rectangular_box_with_three_triangularly_arranged_through_holes"

# Parameters
length = 100.0
width = 60.0
height = 20.0
hole_radius = 5.0
side_length = 30.0

# Compute box center in XY
center = App.Vector(length/2.0, width/2.0, 0.0)

# Calculate distance from centroid to triangle vertices
d = side_length / math.sqrt(3.0)
angles_deg = [90.0, 210.0, 330.0]

# Generate hole positions relative to box center
hole_positions = []
for angle in angles_deg:
    rad = math.radians(angle)
    x = d * math.cos(rad)
    y = d * math.sin(rad)
    hole_positions.append(App.Vector(x, y, 0.0))

# Create the base box
box_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))
box_obj = doc.addObject("Part::Feature", "Box")
box_obj.Shape = box_shape

# Create cylinders for holes
cylinder_shapes = []
for idx, pos in enumerate(hole_positions, start=1):
    cyl = Part.makeCylinder(hole_radius, height, center + pos)
    cyl_obj = doc.addObject("Part::Feature", f"Hole_Cylinder_{idx}")
    cyl_obj.Shape = cyl
    cylinder_shapes.append(cyl)

# Perform boolean cuts
result_shape = box_obj.Shape
for cyl in cylinder_shapes:
    result_shape = result_shape.cut(cyl)

# Create the final feature
final_obj = doc.addObject("Part::Feature", "box_with_3_holes")
final_obj.Shape = result_shape

# Recompute to finalize
doc.recompute()

# Export to STEP
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
step_path = os.path.join(output_dir, "Rectangular_box_with_three_triangularly_arranged_through_holes.step")
Import.export([final_obj], step_path)

#example Rectangular with 4 corner holes and chamfered edges
import FreeCAD as App
import Part
import os
import Import

# Create a new, headless FreeCAD document
doc = App.newDocument("GeneratedModel")
doc.Label = "Rectangular_box_with_4_corner_holes_and_chamfered_edges"

# -----------------------------
# Parameters (for easy tuning)
# -----------------------------
length       = 100.0   # X dimension of the box
width        = 60.0    # Y dimension of the box
height       = 20.0    # Z dimension of the box
hole_radius  = 5.0     # Radius of each corner hole
chamfer_size = 2.0     # Size of the chamfer on all external edges

# --------------------------------------
# 1) Create the base rectangular box
# --------------------------------------
base_box_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))

# --------------------------------------
# 2) Chamfer all external edges of box
# --------------------------------------
# Get all edges of the box
edges = base_box_shape.Edges
# Apply chamfer using a single radius for all edges
chamfered_box = base_box_shape.makeChamfer(chamfer_size, edges)

# --------------------------------------
# 3) Define corner-hole cylinder positions
# --------------------------------------
hole_positions = [
    App.Vector(10.0, 10.0, 0.0),
    App.Vector(length - 10.0, 10.0, 0.0),
    App.Vector(10.0, width  - 10.0, 0.0),
    App.Vector(length - 10.0, width  - 10.0, 0.0),
]

# --------------------------------------
# 4) Subtract four through-holes
# --------------------------------------
result_shape = chamfered_box
for idx, pos in enumerate(hole_positions, start=1):
    cyl = Part.makeCylinder(hole_radius, height, pos, App.Vector(0.0, 0.0, 1.0))
    result_shape = result_shape.cut(cyl)

# --------------------------------------
# 5) Create the final Part::Feature object
# --------------------------------------
final_obj = doc.addObject("Part::Feature",
                          "Rectangular_Box_With_4_Corner_Holes_and_Chamfered_Edges")
final_obj.Shape = result_shape

# Recompute document to finalize all operations
doc.recompute()

# --------------------------------------
# 6) Export the final shape to STEP
# --------------------------------------
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
step_path = os.path.join(
    output_dir,
    "Rectangular_box_with_4_corner_holes_and_chamfered_edges.step"
)
Import.export([final_obj], step_path)

# --------------------------------------
# Script complete: the STEP file is ready
# --------------------------------------

#Example rectangular with chamfered edges
import FreeCAD as App
import Part
import os
import Import

# -----------------------------
# Parameters (for easy tuning)
# -----------------------------
length       = 100.0   # X dimension of the box
width        = 50.0    # Y dimension of the box
height       = 20.0    # Z dimension of the box
chamfer_size = 2.0     # Chamfer size on all external edges (2 mm at 45°)

# -----------------------------
# 1) Create a new, headless document
# -----------------------------
doc = App.newDocument("GeneratedModel")
doc.Label = "Rectangular_block_with_chamfered_edges"

# -----------------------------
# 2) Create the base rectangular box
# -----------------------------
base_box_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))
base_box_obj   = doc.addObject("Part::Feature", "BaseBox")
base_box_obj.Shape = base_box_shape

# -----------------------------
# 3) Apply chamfer to all external edges
# -----------------------------
# Collect all edges of the box
edges = base_box_shape.Edges

# Perform chamfer operation
chamfered_shape = base_box_shape.makeChamfer(chamfer_size, edges)

# Create a new feature for the chamfered box
chamfered_box_obj = doc.addObject("Part::Feature", "ChamferedBox")
chamfered_box_obj.Shape = chamfered_shape

# -----------------------------
# 4) Finalize the model
# -----------------------------
doc.recompute()

# -----------------------------
# 5) Export to STEP
# -----------------------------
output_dir = "cad_outputs"
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
step_path = os.path.join(output_dir, "Rectangular_block_with_chamfered_edges.step")
Import.export([chamfered_box_obj], step_path)

# Final recompute to ensure integrity
doc.recompute()

#Example Perforlated sheet C20_U40_1200x600
#!/usr/bin/env python3
import FreeCAD as App
import Part
import Import
import Mesh
import os

# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "C20_U40_1200x600_Optimized"  # DO NOT CHANGE THIS VALUE (Added _Optimized for clarity)

# =========================
# Document Setup
# =========================
doc = App.newDocument("GeneratedModel")
doc.Label = sanitized_title

# =========================
# Parameters (mm)
# =========================
length = 1200.0  # X dimension (length)
width  =  600.0  # Y dimension (width)
height =    2.0  # Z dimension (thickness)

# =========================
# Create the metal plate
# =========================
base_plate = Part.makeBox(length,
                          width,
                          height,
                          App.Vector(0.0, 0.0, 0.0))

# =========================
# Drill square holes (evenly distribute holes with equal margins on both edges)
#   - square side: 20 mm
#   - center spacing: 40 mm in X and Y
# =========================
hole_side = 20.0
spacing   = 40.0
tool_height = height * 1.5 # Make tool slightly thicker for robust cutting

# Compute number of holes in X direction
if length < hole_side:
    n_x = 0
else:
    n_x = int((length - hole_side) // spacing) + 1

# Total span occupied by holes
if n_x > 0:
    span_x = (n_x - 1) * spacing + hole_side
    # Margin on each side to center the holes
    margin_x = (length - span_x) / 2.0
else:
    margin_x = length / 2.0 # Or handle as no holes

# Build X centers array
x_centers = []
if n_x > 0:
    x_centers = [
        margin_x + hole_side / 2.0 + i * spacing
        for i in range(n_x)
    ]

# Compute number of holes in Y direction
if width < hole_side:
    n_y = 0
else:
    n_y = int((width - hole_side) // spacing) + 1

if n_y > 0:
    span_y = (n_y - 1) * spacing + hole_side
    margin_y = (width - span_y) / 2.0
else:
    margin_y = width / 2.0 # Or handle as no holes

# Build Y centers array
y_centers = []
if n_y > 0:
    y_centers = [
        margin_y + hole_side / 2.0 + j * spacing
        for j in range(n_y)
    ]

# Start with the base plate shape
result_shape = base_plate
all_hole_tools = [] # List to store all hole cutting tools

# For each hole center, create a square prism and add to list
if n_x > 0 and n_y > 0:
    for cx in x_centers:
        for cy in y_centers:
            # Boundary check (optional, as centering logic should handle it, but good for robustness)
            # Ensure the entire hole is within the plate boundaries
            min_hole_x = cx - hole_side / 2.0
            max_hole_x = cx + hole_side / 2.0
            min_hole_y = cy - hole_side / 2.0
            max_hole_y = cy + hole_side / 2.0
            tolerance = 1e-6

            if (min_hole_x >= 0.0 - tolerance and
                max_hole_x <= length + tolerance and
                min_hole_y >= 0.0 - tolerance and
                max_hole_y <= width + tolerance):

                hole_corner = App.Vector(cx - hole_side / 2.0,
                                         cy - hole_side / 2.0,
                                         (height - tool_height) / 2.0) # Center tool vertically for good measure
                hole_box = Part.makeBox(hole_side,
                                        hole_side,
                                        tool_height, # Use slightly thicker tool
                                        hole_corner)
                all_hole_tools.append(hole_box)

# If there are tools, create a compound and cut once
if all_hole_tools:
    holes_compound = Part.Compound(all_hole_tools)
    result_shape = base_plate.cut(holes_compound)

# =========================
# Create the final Part Feature
# =========================
plate_obj = doc.addObject("Part::Feature", "MetalPlateWithHoles")
plate_obj.Label = sanitized_title
plate_obj.Shape = result_shape

# Recompute to finalize geometry
doc.recompute()

# =========================
# Export settings (absolute paths)
# =========================
# Determine script directory robustly
if "__file__" in locals() or "__file__" in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    # Fallback for environments where __file__ is not defined (e.g. FreeCAD GUI Python console)
    script_dir = os.getcwd() 

output_dir_abs = os.path.abspath(os.path.join(script_dir,
                                              '..',
                                              'cad_outputs_generated'))
os.makedirs(output_dir_abs, exist_ok=True)

# STEP export
step_filename_abs = os.path.join(output_dir_abs,
                                 f'{sanitized_title}.step')
Import.export([plate_obj], step_filename_abs)
print(f"Model exported to {step_filename_abs}")

# OBJ export
obj_filename_abs = os.path.join(output_dir_abs,
                                f'{sanitized_title}.obj')
Mesh.export([plate_obj], obj_filename_abs)
print(f"Model exported to {obj_filename_abs}")

print(f"Generated {sanitized_title}")


#Example perforated sheet LR5x20 Z9x24 600x300
#!/usr/bin/env python3
import FreeCAD as App
import Part
import os
import Import
import Mesh

# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "LR5x20_Z9x24_600x300"  # DO NOT CHANGE THIS VALUE

# =========================
# Document Setup
# =========================
doc = App.newDocument("GeneratedModel")
doc.Label = sanitized_title

# =========================
# Parameters (mm) for the plate
# =========================
plate_length = 600.0  # X dimension
plate_width  = 300.0  # Y dimension
plate_thickness = 2.0  # Z dimension

# =========================
# Create the base metal plate
# =========================
base_plate_shape = Part.makeBox(plate_length,
                                plate_width,
                                plate_thickness,
                                App.Vector(0.0, 0.0, 0.0))

# =========================
# Parameters for the obround holes (e.g., LR5x20 type)
# =========================
# "Horizontally oriented" means length of obround is along plate's X-axis.
obround_hole_length = 20.0  # Total length of the obround (along plate X)
obround_hole_width  = 5.0   # Total width of the obround (along plate Y)

# =========================
# Parameters for the staggered pattern
# =========================
# "horizontal spacing between adjacent holes" -> pitch_x (center-to-center in X for non-staggered elements)
# "vertical offset between rows" -> pitch_y (center-to-center in Y between rows of holes)
# These pitch values can be adjusted to change pattern density.
pitch_x = 24.0  # Center-to-center spacing in X-direction for holes in the same conceptual line
pitch_y = 9.0  # Center-to-center spacing in Y-direction between rows of holes

# =========================
# Helper function to create a single obround tool
# Creates an obround in the XY plane, centered at (cx, cy), extruded by 'thickness_z' along Z.
# total_len is aligned with the X-axis, obround_h with the Y-axis.
# =========================
def make_single_obround_tool(cx, cy, total_len, obround_h, thickness_z):
    radius = obround_h / 2.0
    # Length of the central rectangular part of the obround
    rect_len = total_len - 2 * radius 

    if rect_len <= 1e-6: # If effectively a circle (e.g. total_len == obround_h)
        tool_shape = Part.makeCylinder(radius, thickness_z, 
                                       App.Vector(cx, cy, 0), 
                                       App.Vector(0,0,1)) # Axis along Z
    else:
        # Central rectangular prism
        rect_base_pos = App.Vector(cx - rect_len / 2.0,
                                   cy - obround_h / 2.0,
                                   0.0)
        rect_box = Part.makeBox(rect_len, obround_h, thickness_z, rect_base_pos)

        # Cylinder at the "positive X" end of the rectangle
        cyl1_center_pos = App.Vector(cx + rect_len / 2.0, cy, 0.0)
        cyl1 = Part.makeCylinder(radius, thickness_z, cyl1_center_pos, App.Vector(0,0,1))

        # Cylinder at the "negative X" end of the rectangle
        cyl2_center_pos = App.Vector(cx - rect_len / 2.0, cy, 0.0)
        cyl2 = Part.makeCylinder(radius, thickness_z, cyl2_center_pos, App.Vector(0,0,1))
        
        tool_shape = rect_box.fuse(cyl1).fuse(cyl2)
    return tool_shape

# =========================
# Calculate hole distribution: number of rows/columns and margins
# =========================

# Max number of holes in X direction (columns of holes) if not staggered
if plate_length < obround_hole_length:
    n_cols_max = 0
else:
    n_cols_max = int((plate_length - obround_hole_length) / pitch_x) + 1
    
# Max number of holes in Y direction (rows of holes)
if plate_width < obround_hole_width:
    n_rows_max = 0
else:
    n_rows_max = int((plate_width - obround_hole_width) / pitch_y) + 1

# Calculate margins to center the overall pattern of n_cols_max by n_rows_max
if n_cols_max > 0:
    span_x = (n_cols_max - 1) * pitch_x + obround_hole_length
    margin_x = (plate_length - span_x) / 2.0
else:
    margin_x = plate_length / 2.0 
    n_cols_max = 0 # Ensure it's 0 if no space

if n_rows_max > 0:
    span_y = (n_rows_max - 1) * pitch_y + obround_hole_width
    margin_y = (plate_width - span_y) / 2.0
else:
    margin_y = plate_width / 2.0
    n_rows_max = 0 # Ensure it's 0 if no space


# =========================
# Create and cut holes from the plate
# =========================
result_shape = base_plate_shape

if n_cols_max > 0 and n_rows_max > 0:
    for j in range(n_rows_max): # j is the row index (iterating in Y direction)
        current_center_y = margin_y + obround_hole_width / 2.0 + j * pitch_y
        
        # Determine X stagger for this row j
        # Alternate rows (e.g., odd j) are staggered by half of pitch_x
        x_stagger_offset = 0.0
        if j % 2 == 1: 
            x_stagger_offset = pitch_x / 2.0

        for i in range(n_cols_max): # i is the column index (iterating in X direction)
            current_center_x_base = margin_x + obround_hole_length / 2.0 + i * pitch_x
            current_center_x = current_center_x_base + x_stagger_offset

            # Boundary check: ensure the entire hole is within the plate
            min_hole_x = current_center_x - obround_hole_length / 2.0
            max_hole_x = current_center_x + obround_hole_length / 2.0
            min_hole_y = current_center_y - obround_hole_width / 2.0
            max_hole_y = current_center_y + obround_hole_width / 2.0
            
            tolerance = 1e-6 # Small tolerance for floating point comparisons

            if (min_hole_x >= 0.0 - tolerance and 
                max_hole_x <= plate_length + tolerance and
                min_hole_y >= 0.0 - tolerance and 
                max_hole_y <= plate_width + tolerance):
                
                hole_tool = make_single_obround_tool(current_center_x, current_center_y,
                                                     obround_hole_length, obround_hole_width,
                                                     plate_thickness)
                result_shape = result_shape.cut(hole_tool)

# =========================
# Create the final Part Feature in the document
# =========================
plate_obj = doc.addObject("Part::Feature", "PerforatedPlateStaggeredObrounds") 
plate_obj.Label = sanitized_title 
plate_obj.Shape = result_shape

doc.recompute()

# =========================
# Export settings (absolute paths)
# =========================
script_dir = os.path.dirname(os.path.abspath(__file__))
output_dir_abs = os.path.abspath(os.path.join(script_dir, "..", "cad_outputs_generated"))
os.makedirs(output_dir_abs, exist_ok=True)

step_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.step')
Import.export([plate_obj], step_filename_abs)
print(f"Model exported to {step_filename_abs}")

obj_filename_abs = os.path.join(output_dir_abs, f'{sanitized_title}.obj')
Mesh.export([plate_obj], obj_filename_abs)
print(f"Model exported to {obj_filename_abs}")

#Example perforated sheet elips holes
#!/usr/bin/env python3
import FreeCAD as App
import Part
import os
import Import
import Mesh

# ----------------------------------------
# Document Setup
# ----------------------------------------
doc = App.newDocument("GeneratedModel")
doc.Label = "Perforated_sheet_with_elliptical_holes"

# ----------------------------------------
# Parameters (mm)
# ----------------------------------------
plate_length    = 300.0   # X dimension of the plate
plate_width     = 200.0   # Y dimension of the plate
plate_thickness = 2.0     # Z dimension (thickness)

major_axis =  30.0  # Ellipse major axis (along X) 
minor_axis =  15.0  # Ellipse minor axis (along Y)

# Pattern parameters – adjust as needed
n_cols    = 10     # number of holes in X-direction
n_rows    =  8     # number of holes in Y-direction
spacing_x = 30.0   # center-to-center spacing in X
spacing_y = 25.0   # center-to-center spacing in Y

# ----------------------------------------
# Compute margins to center the pattern
# ----------------------------------------
if n_cols > 0:
    span_x   = (n_cols - 1) * spacing_x + major_axis
    margin_x = (plate_length - span_x) / 2.0
else:
    margin_x = plate_length / 2.0

if n_rows > 0:
    span_y   = (n_rows - 1) * spacing_y + minor_axis
    margin_y = (plate_width - span_y) / 2.0
else:
    margin_y = plate_width / 2.0

# ----------------------------------------
# Create the base plate
# ----------------------------------------
base_plate = Part.makeBox(
    plate_length,
    plate_width,
    plate_thickness,
    App.Vector(0.0, 0.0, 0.0)
)

# ----------------------------------------
# Helper: make an extruded elliptical hole
# ----------------------------------------
def make_elliptical_hole(cx, cy, maj, mino, thickness):
    """
    Creates an extruded ellipse (solid) centered at (cx,cy) in XY,
    extruded along Z by 'thickness'.
    """
    # 1) create the 2D ellipse curve at origin
    ell = Part.Ellipse()
    ell.MajorRadius = maj / 2.0
    ell.MinorRadius = mino / 2.0
    edge = ell.toShape()
    wire = Part.Wire([edge])
    face = Part.Face(wire)
    # 2) extrude face along Z
    solid = face.extrude(App.Vector(0, 0, thickness))
    # 3) translate to (cx, cy, 0)
    solid.translate(App.Vector(cx, cy, 0.0))
    return solid

# ----------------------------------------
# Generate all hole tools
# ----------------------------------------
hole_tools = []
tol = 1e-6
for j in range(n_rows):
    cy = margin_y + minor_axis / 2.0 + j * spacing_y
    for i in range(n_cols):
        cx = margin_x + major_axis / 2.0 + i * spacing_x
        # boundary check
        if (cx - major_axis/2.0 >= -tol and
            cx + major_axis/2.0 <= plate_length + tol and
            cy - minor_axis/2.0 >= -tol and
            cy + minor_axis/2.0 <= plate_width + tol):
            hole = make_elliptical_hole(cx, cy,
                                       major_axis,
                                       minor_axis,
                                       plate_thickness)
            hole_tools.append(hole)

# ----------------------------------------
# Perform boolean cut (all holes at once)
# ----------------------------------------
if hole_tools:
    compound_holes = Part.makeCompound(hole_tools)
    final_shape = base_plate.cut(compound_holes)
else:
    final_shape = base_plate

# ----------------------------------------
# Create the final Part::Feature
# ----------------------------------------
plate_obj = doc.addObject("Part::Feature", "PerforatedSheet")
plate_obj.Label = "Perforated_sheet_with_elliptical_holes"
plate_obj.Shape = final_shape

doc.recompute()

# ----------------------------------------
# Export to STEP and OBJ
# ----------------------------------------
# Determine base directory for exports (one level up from script)
if "__file__" in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    script_dir = os.getcwd()

output_dir = os.path.abspath(os.path.join(script_dir, "..", "cad_outputs_generated"))
os.makedirs(output_dir, exist_ok=True)

step_path = os.path.join(output_dir, "Perforated_sheet_with_elliptical_holes.step")
Import.export([plate_obj], step_path)
print(f"Model exported to {step_path}")

obj_path = os.path.join(output_dir, "Perforated_sheet_with_elliptical_holes.obj")
Mesh.export([plate_obj], obj_path)
print(f"Model exported to {obj_path}")

#Example perforated sheet elips stagger grid pattern
#!/usr/bin/env python3
import FreeCAD as App
import Part
import os
import Import
import Mesh

# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "Perforated_sheet_with_elliptical_holes"  # DO NOT CHANGE THIS VALUE

# ----------------------------------------
# Document Setup
# ----------------------------------------
doc = App.newDocument("GeneratedModel")
doc.Label = sanitized_title

# ----------------------------------------
# Parameters (mm) – updated for user request
# ----------------------------------------
plate_length    = 1000.0   # X dimension of the sheet
plate_width     = 500.0    # Y dimension of the sheet
plate_thickness = 2.0      # Z dimension (thickness)

major_axis = 30.0  # Ellipse major axis (along X)
minor_axis = 15.0  # Ellipse minor axis (along Y)

# Pattern parameters – reuse or adjust as needed
n_cols    = 10     # number of holes in X-direction
n_rows    = 8      # number of holes in Y-direction
spacing_x = 80.0   # center-to-center spacing in X
spacing_y = 50.0   # center-to-center spacing in Y

# ----------------------------------------
# Compute margins to center the pattern
# ----------------------------------------
if n_cols > 0:
    span_x   = (n_cols - 1) * spacing_x + major_axis
    margin_x = (plate_length - span_x) / 2.0
else:
    margin_x = plate_length / 2.0

if n_rows > 0:
    span_y   = (n_rows - 1) * spacing_y + minor_axis
    margin_y = (plate_width - span_y) / 2.0
else:
    margin_y = plate_width / 2.0

# ----------------------------------------
# Create the base plate
# ----------------------------------------
base_plate = Part.makeBox(
    plate_length,
    plate_width,
    plate_thickness,
    App.Vector(0.0, 0.0, 0.0)
)

# ----------------------------------------
# Helper: create an extruded elliptical hole
# ----------------------------------------
def make_elliptical_hole(cx, cy, maj, mino, thickness):
    """
    Returns a solid ellipse extruded along Z by 'thickness',
    centered at (cx, cy).
    """
    ell = Part.Ellipse()
    ell.MajorRadius = maj / 2.0
    ell.MinorRadius = mino / 2.0
    edge = ell.toShape()
    wire = Part.Wire([edge])
    face = Part.Face(wire)
    solid = face.extrude(App.Vector(0, 0, thickness))
    solid.translate(App.Vector(cx, cy, 0.0))
    return solid

# ----------------------------------------
# Build all hole tools in a staggered grid pattern
# ----------------------------------------
hole_tools = []
tol = 1e-6
for row in range(n_rows):
    # Y position for this row
    cy = margin_y + minor_axis / 2.0 + row * spacing_y
    # Stagger offset: alternate rows shifted by half spacing_x
    if row % 2 == 1:
        x_stagger = spacing_x / 2.0
    else:
        x_stagger = 0.0
    for col in range(n_cols):
        # X position with stagger
        cx = margin_x + major_axis / 2.0 + col * spacing_x + x_stagger
        # Boundary check to ensure hole fully on plate
        min_x = cx - major_axis / 2.0
        max_x = cx + major_axis / 2.0
        min_y = cy - minor_axis / 2.0
        max_y = cy + minor_axis / 2.0
        if (min_x >= -tol and max_x <= plate_length + tol and
            min_y >= -tol and max_y <= plate_width + tol):
            hole = make_elliptical_hole(cx, cy, major_axis, minor_axis, plate_thickness)
            hole_tools.append(hole)

# ----------------------------------------
# Boolean cut: subtract all holes at once
# ----------------------------------------
if hole_tools:
    compound_holes = Part.makeCompound(hole_tools)
    result_shape = base_plate.cut(compound_holes)
else:
    result_shape = base_plate

# ----------------------------------------
# Create and register final object
# ----------------------------------------
plate_obj = doc.addObject("Part::Feature", "PerforatedSheet")
plate_obj.Label = sanitized_title
plate_obj.Shape = result_shape
doc.recompute()

# ----------------------------------------
# Export to STEP and OBJ using absolute paths
# ----------------------------------------
if "__file__" in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    script_dir = os.getcwd()

output_dir_abs = os.path.abspath(os.path.join(script_dir, "..", "cad_outputs_generated"))
os.makedirs(output_dir_abs, exist_ok=True)

# STEP export
step_filename_abs = os.path.join(output_dir_abs, f"{sanitized_title}.step")
Import.export([plate_obj], step_filename_abs)
print(f"Model exported to {step_filename_abs}")

# OBJ export
obj_filename_abs = os.path.join(output_dir_abs, f"{sanitized_title}.obj")
Mesh.export([plate_obj], obj_filename_abs)
print(f"Model exported to {obj_filename_abs}")


#Example perforated sheet R12 T16 1x500x1000
#!/usr/bin/env python3
import FreeCAD as App
import Part
import os
import Import
import Mesh

# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "Perforated_sheet_R12_T16"  # DO NOT CHANGE THIS VALUE

# ----------------------------------------
# Document Setup
# ----------------------------------------
doc = App.newDocument("GeneratedModel")
doc.Label = sanitized_title

# ----------------------------------------
# Parameters (mm)
# ----------------------------------------
plate_length    = 500.0   # X dimension of the sheet
plate_width     = 1000.0   # Y dimension of the sheet
plate_thickness = 1.0     # Z dimension (thickness)

hole_diameter = 12.0      # Hole diameter Ø12 mm
hole_radius   = hole_diameter / 2.0

pitch_x = 16.0            # center-to-center spacing in X
pitch_y = 16.0            # center-to-center spacing in Y

tol = 1e-6                # tolerance for boundary checks

# ----------------------------------------
# Compute number of holes and margins
# ----------------------------------------
if plate_length < hole_diameter:
    n_cols = 0
else:
    n_cols = int((plate_length - hole_diameter) / pitch_x) + 1
if n_cols > 0:
    span_x = (n_cols - 1) * pitch_x + hole_diameter
    margin_x = (plate_length - span_x) / 2.0
else:
    margin_x = plate_length / 2.0

if plate_width < hole_diameter:
    n_rows = 0
else:
    n_rows = int((plate_width - hole_diameter) / pitch_y) + 1
if n_rows > 0:
    span_y = (n_rows - 1) * pitch_y + hole_diameter
    margin_y = (plate_width - span_y) / 2.0
else:
    margin_y = plate_width / 2.0

# ----------------------------------------
# Create the base plate
# ----------------------------------------
base_plate = Part.makeBox(
    plate_length,
    plate_width,
    plate_thickness,
    App.Vector(0.0, 0.0, 0.0)
)

# ----------------------------------------
# Build all hole tools in a staggered grid pattern
# ----------------------------------------
hole_tools = []
for j in range(n_rows):
    # Y position for this row
    cy = margin_y + hole_radius + j * pitch_y
    # Stagger offset: alternate rows shifted by half pitch_x
    if j % 2 == 1:
        x_stagger = pitch_x / 2.0
    else:
        x_stagger = 0.0
    for i in range(n_cols):
        # X position with stagger
        cx = margin_x + hole_radius + i * pitch_x + x_stagger
        # Boundary check to ensure hole fully on plate
        min_x = cx - hole_radius
        max_x = cx + hole_radius
        min_y = cy - hole_radius
        max_y = cy + hole_radius
        if (min_x >= -tol and max_x <= plate_length + tol and
            min_y >= -tol and max_y <= plate_width + tol):
            cyl = Part.makeCylinder(
                hole_radius,
                plate_thickness,
                App.Vector(cx, cy, 0.0),
                App.Vector(0.0, 0.0, 1.0)
            )
            hole_tools.append(cyl)

# ----------------------------------------
# Boolean cut: subtract all holes at once
# ----------------------------------------
if hole_tools:
    compound_holes = Part.makeCompound(hole_tools)
    result_shape = base_plate.cut(compound_holes)
else:
    result_shape = base_plate

# ----------------------------------------
# Create and register final object
# ----------------------------------------
plate_obj = doc.addObject("Part::Feature", "PerforatedSheet")
plate_obj.Label = sanitized_title
plate_obj.Shape = result_shape

doc.recompute()

# ----------------------------------------
# Export to STEP and OBJ using absolute paths
# ----------------------------------------
output_dir_abs = os.path.abspath(
    os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')
)
os.makedirs(output_dir_abs, exist_ok=True)

# STEP export
step_filename_abs = os.path.join(
    output_dir_abs, f"{sanitized_title}.step"
)
Import.export([plate_obj], step_filename_abs)
print(f"Model exported to {step_filename_abs}")

# OBJ export
obj_filename_abs = os.path.join(
    output_dir_abs, f"{sanitized_title}.obj"
)
Mesh.export([plate_obj], obj_filename_abs)
print(f"Model exported to {obj_filename_abs}")

#Example perforated sheet R0.54 T1 1x500x1000
#!/usr/bin/env python3
import FreeCAD as App
import Part
import os
import Import
import Mesh

# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "Perforated_sheet_R12_T16"  # DO NOT CHANGE THIS VALUE

# ----------------------------------------
# Document Setup
# ----------------------------------------
doc = App.newDocument("GeneratedModel")
doc.Label = sanitized_title

# ----------------------------------------
# Parameters (mm)
# ----------------------------------------
plate_length    = 500.0   # X dimension of the sheet
plate_width     = 1000.0   # Y dimension of the sheet
plate_thickness = 1.0     # Z dimension (thickness)

hole_diameter = 0.54     # Hole diameter Ø12 mm
hole_radius   = hole_diameter / 2.0

pitch_x = 1.0            # center-to-center spacing in X
pitch_y = 1.0            # center-to-center spacing in Y

tol = 1e-6                # tolerance for boundary checks

# ----------------------------------------
# Compute number of holes and margins
# ----------------------------------------
if plate_length < hole_diameter:
    n_cols = 0
else:
    n_cols = int((plate_length - hole_diameter) / pitch_x) + 1
if n_cols > 0:
    span_x = (n_cols - 1) * pitch_x + hole_diameter
    margin_x = (plate_length - span_x) / 2.0
else:
    margin_x = plate_length / 2.0

if plate_width < hole_diameter:
    n_rows = 0
else:
    n_rows = int((plate_width - hole_diameter) / pitch_y) + 1
if n_rows > 0:
    span_y = (n_rows - 1) * pitch_y + hole_diameter
    margin_y = (plate_width - span_y) / 2.0
else:
    margin_y = plate_width / 2.0

# ----------------------------------------
# Create the base plate
# ----------------------------------------
base_plate = Part.makeBox(
    plate_length,
    plate_width,
    plate_thickness,
    App.Vector(0.0, 0.0, 0.0)
)

# ----------------------------------------
# Build all hole tools in a staggered grid pattern
# ----------------------------------------
hole_tools = []
for j in range(n_rows):
    # Y position for this row
    cy = margin_y + hole_radius + j * pitch_y
    # Stagger offset: alternate rows shifted by half pitch_x
    if j % 2 == 1:
        x_stagger = pitch_x / 2.0
    else:
        x_stagger = 0.0
    for i in range(n_cols):
        # X position with stagger
        cx = margin_x + hole_radius + i * pitch_x + x_stagger
        # Boundary check to ensure hole fully on plate
        min_x = cx - hole_radius
        max_x = cx + hole_radius
        min_y = cy - hole_radius
        max_y = cy + hole_radius
        if (min_x >= -tol and max_x <= plate_length + tol and
            min_y >= -tol and max_y <= plate_width + tol):
            cyl = Part.makeCylinder(
                hole_radius,
                plate_thickness,
                App.Vector(cx, cy, 0.0),
                App.Vector(0.0, 0.0, 1.0)
            )
            hole_tools.append(cyl)

# ----------------------------------------
# Boolean cut: subtract all holes at once
# ----------------------------------------
if hole_tools:
    compound_holes = Part.makeCompound(hole_tools)
    result_shape = base_plate.cut(compound_holes)
else:
    result_shape = base_plate

# ----------------------------------------
# Create and register final object
# ----------------------------------------
plate_obj = doc.addObject("Part::Feature", "PerforatedSheet")
plate_obj.Label = sanitized_title
plate_obj.Shape = result_shape

doc.recompute()

# ----------------------------------------
# Export to STEP and OBJ using absolute paths
# ----------------------------------------
output_dir_abs = os.path.abspath(
    os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')
)
os.makedirs(output_dir_abs, exist_ok=True)

# STEP export
step_filename_abs = os.path.join(
    output_dir_abs, f"{sanitized_title}.step"
)
Import.export([plate_obj], step_filename_abs)
print(f"Model exported to {step_filename_abs}")

# OBJ export
obj_filename_abs = os.path.join(
    output_dir_abs, f"{sanitized_title}.obj"
)
Mesh.export([plate_obj], obj_filename_abs)
print(f"Model exported to {obj_filename_abs}")

#Emxample perforated sheet R0.57 T1 20x15x1
#!/usr/bin/env python3
import FreeCAD as App
import Part
import os
import Import
import Mesh

# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "Perforated_sheet_R0.57_T1_20x15x1"  # DO NOT CHANGE THIS VALUE

# ----------------------------------------
# Document Setup
# ----------------------------------------
doc = App.newDocument("GeneratedModel")
doc.Label = sanitized_title

# ----------------------------------------
# Parameters (mm)
# ----------------------------------------
plate_length    = 20.0    # X dimension of the sheet
plate_width     = 15.0    # Y dimension of the sheet
plate_thickness = 1.0     # Z dimension (thickness)

hole_diameter   = 0.57    # Diameter of each round hole
hole_radius     = hole_diameter / 2.0

spacing_x       = 1.0     # center‐to‐center spacing in X
spacing_y       = 1.0     # center‐to‐center spacing in Y

tol = 1e-6                # tolerance for boundary checks

# ----------------------------------------
# Compute number of holes and margins
# ----------------------------------------
# Columns
if plate_length < hole_diameter:
    n_cols = 0
else:
    n_cols = int((plate_length - hole_diameter) / spacing_x) + 1

if n_cols > 0:
    span_x   = (n_cols - 1) * spacing_x + hole_diameter
    margin_x = (plate_length - span_x) / 2.0
else:
    margin_x = plate_length / 2.0

# Rows
if plate_width < hole_diameter:
    n_rows = 0
else:
    n_rows = int((plate_width - hole_diameter) / spacing_y) + 1

if n_rows > 0:
    span_y   = (n_rows - 1) * spacing_y + hole_diameter
    margin_y = (plate_width - span_y) / 2.0
else:
    margin_y = plate_width / 2.0

# ----------------------------------------
# Create the base plate
# ----------------------------------------
base_plate = Part.makeBox(
    plate_length,
    plate_width,
    plate_thickness,
    App.Vector(0.0, 0.0, 0.0)
)

# ----------------------------------------
# Build all hole tools in a staggered grid pattern
# ----------------------------------------
hole_tools = []
for row in range(n_rows):
    # Y position for this row
    cy = margin_y + hole_radius + row * spacing_y

    # Stagger offset: every other row shifted by half spacing_x
    if row % 2 == 1:
        x_stagger = spacing_x / 2.0
    else:
        x_stagger = 0.0

    for col in range(n_cols):
        # X position with optional stagger
        cx = margin_x + hole_radius + col * spacing_x + x_stagger

        # Boundary check to ensure hole fully on plate
        min_x = cx - hole_radius
        max_x = cx + hole_radius
        min_y = cy - hole_radius
        max_y = cy + hole_radius

        if (min_x >= -tol and max_x <= plate_length + tol and
            min_y >= -tol and max_y <= plate_width + tol):
            cyl = Part.makeCylinder(
                hole_radius,
                plate_thickness,
                App.Vector(cx, cy, 0.0),
                App.Vector(0.0, 0.0, 1.0)
            )
            hole_tools.append(cyl)

# ----------------------------------------
# Boolean cut: subtract all holes at once
# ----------------------------------------
if hole_tools:
    compound_holes = Part.makeCompound(hole_tools)
    result_shape = base_plate.cut(compound_holes)
else:
    result_shape = base_plate

# ----------------------------------------
# Create and register final object
# ----------------------------------------
plate_obj = doc.addObject("Part::Feature", "PerforatedSheet")
plate_obj.Label = sanitized_title
plate_obj.Shape = result_shape
doc.recompute()

# ----------------------------------------
# Export to STEP and OBJ using absolute paths
# ----------------------------------------
# Determine script directory robustly
if "__file__" in globals():
    script_dir = os.path.dirname(os.path.abspath(__file__))
else:
    script_dir = os.getcwd()

output_dir_abs = os.path.abspath(
    os.path.join(script_dir, "..", "cad_outputs_generated")
)
os.makedirs(output_dir_abs, exist_ok=True)

# STEP export
step_filename_abs = os.path.join(
    output_dir_abs, f"{sanitized_title}.step"
)
Import.export([plate_obj], step_filename_abs)
print(f"Model exported to {step_filename_abs}")

# OBJ export
obj_filename_abs = os.path.join(
    output_dir_abs, f"{sanitized_title}.obj"
)
Mesh.export([plate_obj], obj_filename_abs)
print(f"Model exported to {obj_filename_abs}")

#Example countersink hole
#!/usr/bin/env python3
import FreeCAD as App
import Part
import os
import Import
import Mesh
import math
 
# Define the sanitized_title variable with the exact value provided to this template
sanitized_title = "Rectangular_block_20x30x40_with_countersink_hole"  # DO NOT CHANGE THIS VALUE
 
# ========================================
# Document Setup
# ========================================
doc = App.newDocument("GeneratedModel")
doc.Label = "Rectangular_block_20x30x40_with_countersink_hole"
 
# ========================================
# Parameters (mm)
# ========================================
length = 20.0   # X dimension
width  = 30.0   # Y dimension
height = 40.0   # Z dimension
 
# Hole / Countersink parameters
hole_diameter       = 4.2
hole_radius         = hole_diameter / 2.0
 
hole_depth          = 12.0        # blind hole depth into the block
 
cs_diameter         = 8.0
cs_radius           = cs_diameter / 2.0
 
cs_angle_deg        = 100.0       # full apex angle of countersink
 
# ========================================
# 1) Create the base rectangular block
# ========================================
base_shape = Part.makeBox(length, width, height, App.Vector(0.0, 0.0, 0.0))
 
# ========================================
# 2) Create the counter-sunk hole
# ========================================
# Determine face location and hole center from BBox: X[20,20], Y[0,30], Z[0,40]
# Face is perpendicular to X-axis at X = length
face_x = length
center_y = width * 0.5
center_z = height * 0.5
 
# 2a) Main cylindrical hole (blind)
cyl = Part.makeCylinder(
    hole_radius,
    hole_depth,
    App.Vector(face_x, center_y, center_z),
    App.Vector(-1, 0, 0)  # drill towards negative X
)
 
# 2b) Countersink cone
# Compute countersink height from geometry: (R_cs - R_hole) / tan(half apex angle)
half_angle_rad = math.radians(cs_angle_deg / 2.0)
cs_height = (cs_radius - hole_radius) / math.tan(half_angle_rad)
cone = Part.makeCone(
    cs_radius,
    hole_radius,
    cs_height,
    App.Vector(face_x, center_y, center_z),
    App.Vector(-1, 0, 0)
)
 
# Combine both cutting shapes into a single compound
cut_features = [cyl, cone]
compound_cut = Part.makeCompound(cut_features)
 
# Perform the cut in one operation
result_shape = base_shape.cut(compound_cut)
 
# ========================================
# 3) Add Part::Feature to document
# ========================================
# Use the original block object for continuity
block_obj = doc.addObject("Part::Feature", "RectangularBlockWithCountersink")
block_obj.Label = "Rectangular block 20x30x40 with center countersink hole"
block_obj.Shape = result_shape
 
# Recompute document to finalize
doc.recompute()
 
# ========================================
# 4) Export to STEP and OBJ using absolute paths
# ========================================
# Build absolute output directory (one level up, folder cad_outputs_generated)
output_dir_abs = os.path.abspath(
    os.path.join(os.path.dirname(__file__), '..', 'cad_outputs_generated')
)
os.makedirs(output_dir_abs, exist_ok=True)
 
# STEP export
step_filename_abs = os.path.join(output_dir_abs, f"{sanitized_title}.step")
Import.export([block_obj], step_filename_abs)
print(f"Model exported to {step_filename_abs}")
 
# OBJ export
obj_filename_abs = os.path.join(output_dir_abs, f"{sanitized_title}.obj")
Mesh.export([block_obj], obj_filename_abs)
print(f"Model exported to {obj_filename_abs}")
