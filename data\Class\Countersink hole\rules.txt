COUNTERSINK HOLE DESIGN RULES AND STANDARDS

=== 5.2 MINIMUM DIMENSIONS ===

1. Sheet thickness ≥ countersink depth + 0.5 mm
   - This ensures sufficient material remains after countersinking
   - Example: For M6 countersunk screw (depth 2.9mm), minimum sheet thickness = 2.9 + 0.5 = 3.4mm

2. Countersink diameter: must be adapted to the screw
   - Example: M6 screw → typical diameter ≈ 11 mm (for head screws)
   - Example: M6 screw → typical diameter ≈ 12.4 mm (for countersunk screws with 90° angle)

=== 5.3 THROUGH / BLIND COUNTERSINKING ===

1. Specify whether the countersink is through-hole or blind
   - Through-hole: Hole goes completely through the material
   - Blind hole: Hole has a specific depth and does not go through

2. Avoid countersinking on walls thinner than 2 mm without reinforcement
   - Thin walls may crack or deform during countersinking
   - Consider adding reinforcement ribs or increasing wall thickness

=== SCREW TYPE CLASSIFICATION ===

HEAD SCREWS:
- Characterized by a protruding head that sits above the surface
- Countersink provides clearance for the head
- Standard angles: typically 90° or 100°

COUNTERSUNK SCREWS:
- Head is designed to sit flush with or below the surface
- Requires precise countersink angle matching
- Standard angles: 90° (Metric), 82° or 100° (sheet metal applications)

=== DESIGN VALIDATION RULES ===

For BLIND holes:
- Validate: hole_depth + countersink_depth < sheet_thickness
- Warning: If total depth ≥ sheet_thickness, it becomes a through-hole
- Ensure sufficient material remains at the bottom

For THROUGH holes:
- Hole extends completely through the material
- Countersink depth still limited by sheet thickness
- Consider exit burr and chamfer requirements

=== STANDARD DIMENSIONS REFERENCE ===

HEAD SCREWS (from table):
M4:  Ød1=4.5,  Ød2=8,    Total depth=8
M5:  Ød1=5.5,  Ød2=10,   Total depth=10  
M6:  Ød1=6.6,  Ød2=11,   Total depth=11
M8:  Ød1=9,    Ød2=15,   Total depth=15
M10: Ød1=11,   Ød2=18,   Total depth=18
M12: Ød1=13.5, Ød2=20,   Total depth=20

COUNTERSUNK SCREWS 90° (from table):
M4:  Ød1=4.5,  Ød2=8.6,  Total depth=2.1
M5:  Ød1=5.5,  Ød2=10.4, Total depth=2.5
M6:  Ød1=6.6,  Ød2=12.4, Total depth=2.9
M8:  Ød1=9,    Ød2=16.4, Total depth=3.7
M10: Ød1=11,   Ød2=20.4, Total depth=4.7
M12: Ød1=13.5, Ød2=24.4, Total depth=5.2
M14: Ød1=15.5, Ød2=27.4, Total depth=5.7
M16: Ød1=17.5, Ød2=32.4, Total depth=7.2
M18: Ød1=20,   Ød2=36.4, Total depth=8.2
M20: Ød1=22,   Ød2=40.4, Total depth=9.2

Where:
- Ød1 = Hole diameter (clearance hole for screw shaft)
- Ød2 = Countersink diameter (for screw head)
- Total depth = Maximum countersink depth

=== INFORMATION GATHERING REQUIREMENTS ===

Before generating countersink hole code, the chatbot MUST gather:

1. SCREW TYPE:
   - Head Screw or Countersunk Screw?
   
2. THREAD SIZE:
   - M4, M5, M6, M8, M10, M12, M14, M16, M18, M20?
   
3. COUNTERSINK ANGLE (for Countersunk screws):
   - 90° (Metric standard)
   - 82° (sheet metal)
   - 100° (sheet metal alternative)
   
4. HOLE TYPE:
   - Through-hole or Blind hole?
   
5. MATERIAL DIMENSIONS:
   - Sheet thickness (for validation)
   - Hole position (x, y coordinates)
   
6. FOR BLIND HOLES ADDITIONALLY:
   - Hole depth (must be < sheet_thickness - countersink_depth)

=== VALIDATION WARNINGS ===

The system should warn if:
- Sheet thickness < countersink_depth + 0.5mm
- For blind holes: hole_depth + countersink_depth ≥ sheet_thickness
- Wall thickness < 2mm without reinforcement
- Countersink diameter > reasonable proportion of sheet dimensions
