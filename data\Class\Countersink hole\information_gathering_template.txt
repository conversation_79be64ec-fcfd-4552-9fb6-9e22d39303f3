COUNTERSIN<PERSON> HOLE INFORMATION GATHERING TEMPLATE

This template ensures the chatbot gathers sufficient information before generating countersink hole code.

=== REQUIRED INFORMATION CHECKLIST ===

□ 1. SCREW TYPE CLASSIFICATION
   Question: "What type of screw will be used?"
   Options: 
   - Head Screw (protruding head above surface)
   - Countersunk Screw (head sits flush with surface)
   
   Follow-up: If user is unsure, ask: "Will the screw head sit above the surface (Head Screw) or flush with the surface (Countersunk Screw)?"

□ 2. THREAD SIZE
   Question: "What is the thread size of the screw?"
   Options: M4, M5, M6, M8, M10, M12, M14, M16, M18, M20
   
   Note: This determines hole diameter (d1) and countersink diameter (d2)

□ 3. COUNTERSINK ANGLE (Only for Countersunk Screws)
   Question: "What countersink angle is required?"
   Options:
   - 90° (Metric standard)
   - 82° (sheet metal applications)
   - 100° (sheet metal alternative)
   
   Default: 90° for Metric screws if not specified

□ 4. HOLE TYPE
   Question: "Should this be a through-hole or blind hole?"
   Options:
   - Through-hole: Goes completely through the material
   - Blind hole: Has a specific depth, does not go through
   
□ 5. MATERIAL SPECIFICATIONS
   Questions:
   - "What is the sheet/material thickness?" (Required for validation)
   - "What are the overall dimensions of the part?" (length x width)
   
□ 6. HOLE POSITIONING
   Questions:
   - "Where should the countersink hole be positioned?" (x, y coordinates)
   - "How many holes are needed?"
   - "If multiple holes, what is the spacing pattern?"

□ 7. BLIND HOLE SPECIFIC (Only if hole_type = blind)
   Question: "What should be the hole depth?" 
   Validation: hole_depth + countersink_depth < sheet_thickness
   Warning: "The hole depth plus countersink depth must be less than the sheet thickness to remain a blind hole."

=== VALIDATION QUESTIONS ===

After gathering basic information, validate with these questions:

□ THICKNESS VALIDATION
   Check: sheet_thickness ≥ countersink_depth + 0.5mm
   If not: "Warning: The sheet thickness ({sheet_thickness}mm) may be too thin for this countersink depth ({countersink_depth}mm). Minimum recommended thickness is {countersink_depth + 0.5}mm. Do you want to proceed anyway?"

□ WALL THICKNESS CHECK
   If material has walls < 2mm: "Warning: Countersinking on walls thinner than 2mm may cause cracking. Consider adding reinforcement or increasing wall thickness. Do you want to proceed?"

□ BLIND HOLE DEPTH CHECK
   If blind hole: Check hole_depth + countersink_depth < sheet_thickness
   If not: "Warning: The specified hole depth ({hole_depth}mm) plus countersink depth ({countersink_depth}mm) equals or exceeds the sheet thickness ({sheet_thickness}mm). This would create a through-hole. Should I change this to a through-hole instead?"

=== EXAMPLE COMPLETE INFORMATION SET ===

Good example of complete information:
- Screw Type: Countersunk Screw
- Thread Size: M6
- Countersink Angle: 90°
- Hole Type: Blind hole
- Sheet Thickness: 5.0mm
- Part Dimensions: 100mm x 80mm
- Hole Position: Center (50mm, 40mm)
- Hole Depth: 2.0mm
- Number of Holes: 1

Derived values (auto-calculated from standards):
- Hole Diameter (d1): 6.6mm
- Countersink Diameter (d2): 12.4mm  
- Countersink Depth: 2.9mm

Validation:
✓ Sheet thickness (5.0mm) > countersink depth + 0.5mm (3.4mm)
✓ Hole depth (2.0mm) + countersink depth (2.9mm) = 4.9mm < sheet thickness (5.0mm)

=== INSUFFICIENT INFORMATION EXAMPLES ===

Bad example 1: "Create a countersink hole for M6"
Missing: Screw type, hole type, material thickness, position, countersink angle

Bad example 2: "I need a countersunk screw hole"  
Missing: Thread size, angle, hole type, material specs, position

Bad example 3: "Make a through countersink hole M8 in 3mm sheet"
Missing: Screw type, position, countersink angle (if countersunk screw)

=== CHATBOT RESPONSE TEMPLATES ===

When information is missing:
"I need more information to create the countersink hole correctly. Please provide:
1. [List specific missing items]
2. [Additional questions based on what was provided]

This ensures the hole will meet manufacturing standards and fit your screw properly."

When information is complete:
"Perfect! I have all the information needed:
- [Summarize all parameters]
- [Show derived values from standards]
- [Confirm validations passed]

I'll now generate the FreeCAD code for your countersink hole."
