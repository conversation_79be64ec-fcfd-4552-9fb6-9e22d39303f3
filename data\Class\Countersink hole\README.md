# Countersink Hole Implementation

This folder contains the complete implementation for countersink hole functionality in the 3D CAD chatbot system.

## Files Overview

### 1. `dictionary.csv`
Contains standardized countersink hole specifications for both Head Screws and Countersunk Screws:

**Head Screws (M4-M12):**
- Protruding head above surface
- Cylindrical countersink recess
- Standard dimensions from engineering tables

**Countersunk Screws (M4-M20, 90°):**
- <PERSON> sits flush with surface
- Conical countersink with 90° angle
- Precise depth calculations for flush mounting

### 2. `rules.txt`
Comprehensive design rules and validation criteria:
- **Rule 5.2**: Minimum dimensions (sheet thickness ≥ countersink depth + 0.5mm)
- **Rule 5.3**: Through/Blind hole specifications and wall thickness requirements
- Standard dimension tables and validation formulas

### 3. `information_gathering_template.txt`
Structured template ensuring the chatbot gathers all necessary information:
- Screw type classification (Head vs Countersunk)
- Thread size selection (M4-M20)
- Countersink angle specification (90°, 82°, 100°)
- Hole type determination (through/blind)
- Material specifications and positioning
- Validation checks and warnings

## Integration with Chatbot System

### 1. **Retrieval System Integration**
- Added to `src/rag/retriever.py` for structured data access
- Integrated with FAISS semantic search
- Automatic parameter derivation from standards

### 2. **Analysis Template Updates**
- Enhanced `src/core/text_to_cad_agent.py` with countersink-specific guidance
- Added validation rules and information gathering requirements
- Integrated with existing perforated sheet and tole workflows

### 3. **Example Code Library**
- Added comprehensive examples to `data/example.txt`:
  - M6 Head Screw (through-hole, cylindrical countersink)
  - M8 Countersunk Screw 90° (blind hole, conical countersink)
- Complete FreeCAD code with validation and error checking

## Usage Examples

### Basic Request
```
User: "Create a countersink hole for M6 screw"
```

**Chatbot Response:**
```
I need more information to create the countersink hole correctly. Please provide:
1. Screw type: Head Screw (protruding head) or Countersunk Screw (flush head)?
2. Hole type: Through-hole or Blind hole?
3. Material thickness and overall dimensions
4. Hole position (x, y coordinates)
```

### Complete Request
```
User: "Create an M8 countersunk screw hole, 90 degrees, through-hole, 
       in a 100x80x5mm sheet, centered position"
```

**Chatbot Response:**
```
Perfect! I have all the information needed:
- Screw Type: Countersunk Screw
- Thread Size: M8
- Countersink Angle: 90°
- Hole Type: Through-hole
- Material: 100x80x5mm sheet
- Position: Center (50mm, 40mm)

Derived values from standards:
- Hole Diameter (d1): 9.0mm
- Countersink Diameter (d2): 16.4mm
- Countersink Depth: 3.7mm

Validation:
✓ Sheet thickness (5.0mm) > countersink depth + 0.5mm (4.2mm)

I'll now generate the FreeCAD code for your countersink hole.
```

## Technical Implementation Details

### 1. **Standards-Based Parameter Derivation**
The system automatically derives hole dimensions from engineering standards:
- Hole diameter (d1) for screw shaft clearance
- Countersink diameter (d2) for head accommodation
- Countersink depth based on screw type and angle

### 2. **Validation System**
Comprehensive validation prevents manufacturing issues:
- Material thickness validation
- Blind hole depth checking
- Wall thickness warnings
- Through-hole verification

### 3. **Code Generation**
Generated FreeCAD code includes:
- Parametric design with clear variable definitions
- Proper boolean operations (base → hole → countersink)
- Validation checks and warning messages
- Export to both STEP and OBJ formats

## Benefits

1. **Prevents Errors**: Comprehensive validation prevents common manufacturing mistakes
2. **Standards Compliance**: All dimensions derived from engineering standards
3. **Complete Information Gathering**: Ensures no critical parameters are missed
4. **Educational**: Provides clear explanations of design rules and standards
5. **Flexible**: Supports both Head Screws and Countersunk Screws with various angles

## Future Enhancements

- Support for additional countersink angles (82°, 100°)
- Imperial thread sizes (UNC, UNF)
- Custom countersink profiles
- Multi-hole patterns and arrays
- Integration with fastener libraries
